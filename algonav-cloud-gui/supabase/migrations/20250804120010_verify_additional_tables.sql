-- Migration: Verification tests for category_levels and profiles tables
-- Description: Comprehensive tests to verify all new table functionality works correctly

-- Test D.1: Verify category_levels table exists
DO $$
BEGIN
  IF to_regclass('public.category_levels') IS NOT NULL THEN
    RAISE NOTICE 'TEST PASS: category_levels table exists';
  ELSE
    RAISE EXCEPTION 'TEST FAIL: category_levels table does not exist';
  END IF;
END$$;

-- Test D.2: Verify profiles table exists
DO $$
BEGIN
  IF to_regclass('public.profiles') IS NOT NULL THEN
    RAISE NOTICE 'TEST PASS: profiles table exists';
  ELSE
    RAISE EXCEPTION 'TEST FAIL: profiles table does not exist';
  END IF;
END$$;

-- Test D.3: Verify <PERSON><PERSON> is active on category_levels
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_class 
    WHERE relname = 'category_levels' AND relrowsecurity = true
  ) THEN
    RAISE NOTICE 'TEST PASS: <PERSON><PERSON> is active on category_levels';
  ELSE
    RAISE EXCEPTION 'TEST FAIL: R<PERSON> is not active on category_levels';
  END IF;
END$$;

-- Test D.4: Verify R<PERSON> is active on profiles
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_class 
    WHERE relname = 'profiles' AND relrowsecurity = true
  ) THEN
    RAISE NOTICE 'TEST PASS: RLS is active on profiles';
  ELSE
    RAISE EXCEPTION 'TEST FAIL: RLS is not active on profiles';
  END IF;
END$$;

-- Test D.5: Verify required_file_types JSON schema validation
DO $$
DECLARE
    valid_count INTEGER;
    total_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO total_count FROM profiles;
    
    SELECT COUNT(*) INTO valid_count 
    FROM profiles 
    WHERE jsonb_typeof(required_file_types) = 'array';
    
    IF valid_count = total_count THEN
        RAISE NOTICE 'TEST PASS: All required_file_types are valid JSON arrays (% records)', total_count;
    ELSE
        RAISE EXCEPTION 'TEST FAIL: Invalid required_file_types found. Valid: %, Total: %', valid_count, total_count;
    END IF;
END$$;

-- Test D.6: Verify foreign key constraints work
DO $$
DECLARE
    test_user_id UUID;
    constraint_works BOOLEAN := FALSE;
BEGIN
    -- Get a test user
    SELECT id INTO test_user_id FROM auth.users LIMIT 1;
    
    IF test_user_id IS NULL THEN
        RAISE EXCEPTION 'TEST FAIL: No users found for FK testing';
    END IF;
    
    -- Try to insert with invalid user_id (should fail)
    BEGIN
        INSERT INTO category_levels (user_id, level_index, label) 
        VALUES ('00000000-0000-0000-0000-000000000000', 999, 'Test');
        RAISE EXCEPTION 'TEST FAIL: FK constraint did not prevent invalid user_id';
    EXCEPTION
        WHEN foreign_key_violation THEN
            constraint_works := TRUE;
        WHEN OTHERS THEN
            RAISE EXCEPTION 'TEST FAIL: Unexpected error: %', SQLERRM;
    END;
    
    IF constraint_works THEN
        RAISE NOTICE 'TEST PASS: Foreign key constraints are working correctly';
    END IF;
END$$;

-- Test E: Smoke test script (CI)
DO $$
DECLARE
    test_user_id UUID;
    level_count INTEGER;
    profile_count INTEGER;
    has_required_types BOOLEAN;
BEGIN
    -- Get test user
    SELECT id INTO test_user_id FROM auth.users LIMIT 1;
    
    IF test_user_id IS NULL THEN
        RAISE EXCEPTION 'TEST FAIL: No users found for smoke test';
    END IF;
    
    -- Insert two levels
    INSERT INTO category_levels(user_id, level_index, label, description)
    VALUES 
        (test_user_id, 2, 'Datensatz', 'Second level for datasets'),
        (test_user_id, 3, 'Messung', 'Third level for measurements')
    ON CONFLICT (user_id, level_index) DO NOTHING;
    
    -- Create profile with specific file types
    INSERT INTO profiles(user_id, name, required_file_types, optional_file_types, default_variables)
    VALUES (
        test_user_id,
        'Flugvermessung',
        '["gnss_rover", "imu"]'::jsonb,
        '["camera", "lidar"]'::jsonb,
        '{"processing_mode": "high_accuracy", "output_format": "kml"}'::jsonb
    )
    ON CONFLICT (user_id, name) DO NOTHING;
    
    -- Verify expectations
    SELECT COUNT(*) INTO level_count 
    FROM category_levels 
    WHERE user_id = test_user_id;
    
    SELECT COUNT(*) INTO profile_count 
    FROM profiles 
    WHERE user_id = test_user_id;
    
    SELECT (required_file_types ?| array['gnss_rover']) INTO has_required_types
    FROM profiles 
    WHERE user_id = test_user_id AND name = 'Flugvermessung';
    
    -- Check results
    IF level_count >= 2 THEN
        RAISE NOTICE 'TEST PASS: User has % category levels', level_count;
    ELSE
        RAISE EXCEPTION 'TEST FAIL: Expected at least 2 levels, got %', level_count;
    END IF;
    
    IF profile_count >= 2 THEN
        RAISE NOTICE 'TEST PASS: User has % profiles', profile_count;
    ELSE
        RAISE EXCEPTION 'TEST FAIL: Expected at least 2 profiles, got %', profile_count;
    END IF;
    
    IF has_required_types THEN
        RAISE NOTICE 'TEST PASS: Profile contains required gnss_rover file type';
    ELSE
        RAISE EXCEPTION 'TEST FAIL: Profile does not contain required gnss_rover file type';
    END IF;
    
    -- Test JSONB operators
    IF EXISTS (
        SELECT 1 FROM profiles 
        WHERE user_id = test_user_id 
        AND required_file_types @> '["gnss_rover"]'::jsonb
    ) THEN
        RAISE NOTICE 'TEST PASS: JSONB containment operator works correctly';
    ELSE
        RAISE EXCEPTION 'TEST FAIL: JSONB containment operator not working';
    END IF;
    
    -- Test default variables access
    IF EXISTS (
        SELECT 1 FROM profiles 
        WHERE user_id = test_user_id 
        AND default_variables->>'processing_mode' = 'high_accuracy'
    ) THEN
        RAISE NOTICE 'TEST PASS: JSONB key access works correctly';
    ELSE
        RAISE EXCEPTION 'TEST FAIL: JSONB key access not working';
    END IF;
    
    RAISE NOTICE 'TEST PASS: All smoke tests completed successfully';
END$$;

-- Test indexes exist
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'ix_cl_user') THEN
    RAISE NOTICE 'TEST PASS: category_levels user index exists';
  ELSE
    RAISE EXCEPTION 'TEST FAIL: category_levels user index missing';
  END IF;
  
  IF EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'ix_prof_req_gin') THEN
    RAISE NOTICE 'TEST PASS: profiles required_file_types GIN index exists';
  ELSE
    RAISE EXCEPTION 'TEST FAIL: profiles required_file_types GIN index missing';
  END IF;
END$$;

-- Test constraint validation
DO $$
DECLARE
    constraint_works BOOLEAN := FALSE;
BEGIN
    -- Try to insert invalid JSON (should fail)
    BEGIN
        INSERT INTO profiles (user_id, name, required_file_types) 
        SELECT id, 'Invalid Test', '{"not": "an array"}'::jsonb
        FROM auth.users LIMIT 1;
        RAISE EXCEPTION 'TEST FAIL: Constraint did not prevent invalid JSON structure';
    EXCEPTION
        WHEN check_violation THEN
            constraint_works := TRUE;
        WHEN OTHERS THEN
            RAISE EXCEPTION 'TEST FAIL: Unexpected error: %', SQLERRM;
    END;
    
    IF constraint_works THEN
        RAISE NOTICE 'TEST PASS: JSON validation constraints are working correctly';
    END IF;
END$$;

RAISE NOTICE 'ALL ADDITIONAL TABLE TESTS PASSED: category_levels and profiles implementation is working correctly';
