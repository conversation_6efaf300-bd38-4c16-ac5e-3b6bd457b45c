-- Migration: Add path column to categories table
-- Description: Adds ltree path column, backfills existing data, and creates indexes

DO $$
BEGIN
  -- Add path column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'categories' AND column_name = 'path'
  ) THEN
    ALTER TABLE categories ADD COLUMN path ltree;
    RAISE NOTICE 'Added path column to categories table';
  ELSE
    RAISE NOTICE 'Path column already exists in categories table';
  END IF;
END$$;

-- Backfill existing categories with path data
-- This uses a recursive CTE to build the materialized path
WITH RECURSIVE tree AS (
  -- Base case: root categories (no parent)
  SELECT 
    id, 
    parent_category_id, 
    id::text::ltree AS computed_path
  FROM categories 
  WHERE parent_category_id IS NULL
  
  UNION ALL
  
  -- Recursive case: child categories
  SELECT 
    c.id, 
    c.parent_category_id, 
    (t.computed_path || c.id::text::ltree) AS computed_path
  FROM categories c
  JOIN tree t ON t.id = c.parent_category_id
)
UPDATE categories 
SET path = tree.computed_path
FROM tree
WHERE categories.id = tree.id
  AND categories.path IS NULL;

-- Make path column NOT NULL after backfill
DO $$
BEGIN
  -- Check if there are any NULL paths remaining
  IF NOT EXISTS (SELECT 1 FROM categories WHERE path IS NULL) THEN
    ALTER TABLE categories ALTER COLUMN path SET NOT NULL;
    RAISE NOTICE 'Set path column to NOT NULL';
  ELSE
    RAISE WARNING 'Cannot set path to NOT NULL - some categories still have NULL paths';
  END IF;
END$$;

-- Create GIST index for efficient ltree operations
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_indexes 
    WHERE schemaname = 'public' 
    AND tablename = 'categories' 
    AND indexname = 'idx_categories_path_gist'
  ) THEN
    CREATE INDEX idx_categories_path_gist ON categories USING GIST (path);
    RAISE NOTICE 'Created GIST index on categories.path';
  ELSE
    RAISE NOTICE 'GIST index on categories.path already exists';
  END IF;
END$$;

-- Create additional index for ancestor queries
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_indexes 
    WHERE schemaname = 'public' 
    AND tablename = 'categories' 
    AND indexname = 'idx_categories_path_btree'
  ) THEN
    CREATE INDEX idx_categories_path_btree ON categories USING BTREE (path);
    RAISE NOTICE 'Created BTREE index on categories.path';
  ELSE
    RAISE NOTICE 'BTREE index on categories.path already exists';
  END IF;
END$$;
