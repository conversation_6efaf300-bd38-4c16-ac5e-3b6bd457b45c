-- Migration: Add optional path column to datasets table
-- Description: Adds ltree path column to datasets and creates trigger to sync with category path

DO $$
BEGIN
  -- Add path column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'datasets' AND column_name = 'path'
  ) THEN
    ALTER TABLE datasets ADD COLUMN path ltree;
    RAISE NOTICE 'Added path column to datasets table';
  ELSE
    RAISE NOTICE 'Path column already exists in datasets table';
  END IF;
END$$;

-- Create trigger function to sync dataset path with category path
CREATE OR REPLACE FUNCTION trg_sync_dataset_path() R<PERSON><PERSON><PERSON> trigger AS $$
BEGIN
    -- Handle INSERT and UPDATE operations
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        -- If dataset has a category, sync the path
        IF NEW.category_id IS NOT NULL THEN
            SELECT path INTO NEW.path 
            FROM categories 
            WHERE id = NEW.category_id;
        ELSE
            -- No category, clear the path
            NEW.path = NULL;
        END IF;
        
        RETURN NEW;
    END IF;
    
    -- Should not reach here
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS sync_dataset_path ON datasets;

-- Create the trigger
CREATE TRIGGER sync_dataset_path 
    BEFORE INSERT OR UPDATE ON datasets
    FOR EACH ROW 
    EXECUTE FUNCTION trg_sync_dataset_path();

-- Create trigger function to update dataset paths when category paths change
CREATE OR REPLACE FUNCTION trg_update_dataset_paths_on_category_change() RETURNS trigger AS $$
BEGIN
    -- Update all datasets in this category and its descendants
    UPDATE datasets 
    SET path = NEW.path
    WHERE category_id = NEW.id;
    
    -- If this was an update and the path changed, update descendant datasets too
    IF TG_OP = 'UPDATE' AND OLD.path IS DISTINCT FROM NEW.path THEN
        UPDATE datasets 
        SET path = c.path
        FROM categories c
        WHERE datasets.category_id = c.id 
        AND c.path <@ NEW.path;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS update_dataset_paths_on_category_change ON categories;

-- Create the trigger on categories table
CREATE TRIGGER update_dataset_paths_on_category_change
    AFTER INSERT OR UPDATE ON categories
    FOR EACH ROW 
    EXECUTE FUNCTION trg_update_dataset_paths_on_category_change();

-- Backfill existing datasets with path data
UPDATE datasets 
SET path = c.path
FROM categories c
WHERE datasets.category_id = c.id;

-- Create index for efficient path queries on datasets
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_indexes 
    WHERE schemaname = 'public' 
    AND tablename = 'datasets' 
    AND indexname = 'idx_datasets_path_gist'
  ) THEN
    CREATE INDEX idx_datasets_path_gist ON datasets USING GIST (path);
    RAISE NOTICE 'Created GIST index on datasets.path';
  ELSE
    RAISE NOTICE 'GIST index on datasets.path already exists';
  END IF;
END$$;

-- Add comments for documentation
COMMENT ON COLUMN datasets.path IS 'Materialized path from category hierarchy, automatically synced with category.path';
COMMENT ON FUNCTION trg_sync_dataset_path() IS 'Automatically syncs dataset path with category path';
COMMENT ON FUNCTION trg_update_dataset_paths_on_category_change() IS 'Updates dataset paths when category paths change';
