-- Migration: Create profiles table
-- Description: Creates table for user-defined processing profiles with file type requirements

BEGIN;

-- Create profiles table if it doesn't exist
CREATE TABLE IF NOT EXISTS profiles (
  id                   BIGSERIAL PRIMARY KEY,
  user_id              UUID     NOT NULL REFERENCES auth.users(id),
  name                 TEXT     NOT NULL,
  description          TEXT,
  required_file_types  JSONB    NOT NULL,               -- e.g. ["gnss_rover"]
  optional_file_types  JSONB    NOT NULL DEFAULT '[]',
  default_variables    JSONB    NOT NULL DEFAULT '{}',
  created_at           TIMESTAMPTZ DEFAULT now(),
  updated_at           TIMESTAMPTZ DEFAULT now(),
  CONSTRAINT uq_profile_name UNIQUE (user_id, name)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS ix_profiles_user_id ON profiles(user_id);
CREATE INDEX IF NOT EXISTS ix_profiles_name ON profiles(name);

-- JSONB indexes for fast ?/@> matching
CREATE INDEX IF NOT EXISTS ix_prof_req_gin
  ON profiles USING GIN (required_file_types jsonb_path_ops);
CREATE INDEX IF NOT EXISTS ix_prof_opt_gin
  ON profiles USING GIN (optional_file_types jsonb_path_ops);
CREATE INDEX IF NOT EXISTS ix_prof_vars_gin
  ON profiles USING GIN (default_variables);

-- Enable RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "p_profiles_rw" ON profiles;
DROP POLICY IF EXISTS "Users can manage their own profiles" ON profiles;

-- Create RLS policy
CREATE POLICY "Users can manage their own profiles" ON profiles
  FOR ALL
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_profiles_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_profiles_updated_at ON profiles;
CREATE TRIGGER update_profiles_updated_at
    BEFORE UPDATE ON profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_profiles_updated_at();

-- Add validation function for JSONB arrays
CREATE OR REPLACE FUNCTION validate_file_types_array(file_types JSONB)
RETURNS BOOLEAN AS $$
BEGIN
    -- Check if it's an array
    IF jsonb_typeof(file_types) != 'array' THEN
        RETURN FALSE;
    END IF;
    
    -- Check if all elements are strings
    RETURN NOT EXISTS (
        SELECT 1 
        FROM jsonb_array_elements(file_types) AS elem
        WHERE jsonb_typeof(elem) != 'string'
    );
END;
$$ LANGUAGE plpgsql;

-- Add check constraints for data validation
ALTER TABLE profiles 
ADD CONSTRAINT check_required_file_types_array 
CHECK (validate_file_types_array(required_file_types));

ALTER TABLE profiles 
ADD CONSTRAINT check_optional_file_types_array 
CHECK (validate_file_types_array(optional_file_types));

ALTER TABLE profiles 
ADD CONSTRAINT check_default_variables_object 
CHECK (jsonb_typeof(default_variables) = 'object');

-- Bootstrap existing users with default profile
INSERT INTO profiles (user_id, name, description, required_file_types, optional_file_types, default_variables)
SELECT 
    id, 
    'Default', 
    'Default processing profile',
    '[]'::jsonb,
    '[]'::jsonb,
    '{}'::jsonb
FROM auth.users
WHERE NOT EXISTS (
    SELECT 1 FROM profiles p WHERE p.user_id = auth.users.id
)
ON CONFLICT (user_id, name) DO NOTHING;

-- Add comments for documentation
COMMENT ON TABLE profiles IS 'User-defined processing profiles with file type requirements';
COMMENT ON COLUMN profiles.name IS 'Profile name, unique per user';
COMMENT ON COLUMN profiles.required_file_types IS 'JSON array of required file types for this profile';
COMMENT ON COLUMN profiles.optional_file_types IS 'JSON array of optional file types for this profile';
COMMENT ON COLUMN profiles.default_variables IS 'JSON object with default variable values for this profile';

COMMIT;
