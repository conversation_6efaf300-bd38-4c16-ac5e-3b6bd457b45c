-- Migration: Create category_levels table
-- Description: Creates table for hierarchical category level definitions per user

BEGIN;

-- Create category_levels table if it doesn't exist
CREATE TABLE IF NOT EXISTS category_levels (
  id          BIGSERIAL PRIMARY KEY,
  user_id     UUID        NOT NULL REFERENCES auth.users(id),
  level_index INT         NOT NULL,                      -- 1 = Root
  label       TEXT        NOT NULL,
  description TEXT,
  created_at  TIMESTAMPTZ DEFAULT now(),
  updated_at  TIMESTAMPTZ DEFAULT now(),
  CONSTRAINT uq_user_level UNIQUE (user_id, level_index)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS ix_cl_user   ON category_levels(user_id);
CREATE INDEX IF NOT EXISTS ix_cl_label  ON category_levels(label);
CREATE INDEX IF NOT EXISTS ix_cl_level_index ON category_levels(level_index);

-- Enable RLS
ALTER TABLE category_levels ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "p_cl_rw" ON category_levels;
DROP POLICY IF EXISTS "Users can manage their own category levels" ON category_levels;

-- Create RLS policy
CREATE POLICY "Users can manage their own category levels" ON category_levels
  FOR ALL
  USING (user_id = auth.uid())
  WITH CHECK (user_id = auth.uid());

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_category_levels_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_category_levels_updated_at ON category_levels;
CREATE TRIGGER update_category_levels_updated_at
    BEFORE UPDATE ON category_levels
    FOR EACH ROW
    EXECUTE FUNCTION update_category_levels_updated_at();

-- Bootstrap existing users with default level
INSERT INTO category_levels (user_id, level_index, label, description)
SELECT 
    id, 
    1, 
    'Campaign', 
    'Default root level for campaign organization'
FROM auth.users
WHERE NOT EXISTS (
    SELECT 1 FROM category_levels cl WHERE cl.user_id = auth.users.id
)
ON CONFLICT (user_id, level_index) DO NOTHING;

-- Add comments for documentation
COMMENT ON TABLE category_levels IS 'Hierarchical category level definitions per user';
COMMENT ON COLUMN category_levels.level_index IS 'Hierarchy level: 1=Root, 2=Second level, etc.';
COMMENT ON COLUMN category_levels.label IS 'Display label for this hierarchy level';
COMMENT ON COLUMN category_levels.description IS 'Optional description of this hierarchy level';

COMMIT;
