-- Migration: Create trigger function and trigger for automatic path management
-- Description: Creates function and trigger to automatically maintain ltree paths

-- Helper function to update descendant paths
CREATE OR REPLACE FUNCTION update_descendant_paths(
    category_id INTEGER,
    old_path ltree,
    new_path ltree
) RETURNS void AS $$
BEGIN
    -- Update all descendant paths
    UPDATE categories
    SET path = new_path || subpath(path, nlevel(old_path))
    WHERE path <@ old_path AND id != category_id;
END;
$$ LANGUAGE plpgsql;

-- Create or replace the trigger function
CREATE OR REPLACE FUNCTION trg_set_path() RET<PERSON>NS trigger AS $$
DECLARE
    parent_path ltree;
BEGIN
    -- Handle INSERT and UPDATE operations
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        -- If this is a root category (no parent)
        IF NEW.parent_category_id IS NULL THEN
            NEW.path = NEW.id::text::ltree;
        ELSE
            -- Get parent's path
            SELECT path INTO parent_path 
            FROM categories 
            WHERE id = NEW.parent_category_id;
            
            -- If parent exists, build path
            IF parent_path IS NOT NULL THEN
                NEW.path = parent_path || NEW.id::text::ltree;
            ELSE
                -- Parent doesn't exist - this should not happen due to FK constraint
                RAISE EXCEPTION 'Parent category % does not exist', NEW.parent_category_id;
            END IF;
        END IF;
        
        -- Note: Descendant path updates are handled by a separate AFTER UPDATE trigger
        
        RETURN NEW;
    END IF;
    
    -- Should not reach here
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Drop existing triggers if they exist
DROP TRIGGER IF EXISTS set_path ON categories;
DROP TRIGGER IF EXISTS update_descendant_paths_trigger ON categories;

-- Create the main trigger for INSERT and UPDATE
CREATE TRIGGER set_path
    BEFORE INSERT OR UPDATE ON categories
    FOR EACH ROW
    EXECUTE FUNCTION trg_set_path();

-- Create a separate AFTER UPDATE trigger for descendant path updates
CREATE OR REPLACE FUNCTION trg_update_descendant_paths() RETURNS trigger AS $$
BEGIN
    -- Only run if parent_category_id changed
    IF OLD.parent_category_id IS DISTINCT FROM NEW.parent_category_id THEN
        -- Update all descendant paths
        PERFORM update_descendant_paths(NEW.id, OLD.path, NEW.path);
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_descendant_paths_trigger
    AFTER UPDATE ON categories
    FOR EACH ROW
    EXECUTE FUNCTION trg_update_descendant_paths();

-- Add comment for documentation
COMMENT ON FUNCTION trg_set_path() IS 'Automatically maintains ltree path column for categories table';
COMMENT ON TRIGGER set_path ON categories IS 'Trigger to automatically update path when categories are inserted or updated';
