-- Migration: Verification tests for ltree implementation
-- Description: Comprehensive tests to verify all ltree functionality works correctly

-- Test 1: Verify ltree extension is enabled
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'ltree') THEN
    RAISE NOTICE 'TEST PASS: ltree extension is enabled';
  ELSE
    RAISE EXCEPTION 'TEST FAIL: ltree extension is not enabled';
  END IF;
END$$;

-- Test 2: Verify categories table has path column with correct type
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'categories' 
    AND column_name = 'path' 
    AND data_type = 'USER-DEFINED'
  ) THEN
    RAISE NOTICE 'TEST PASS: categories.path column exists with ltree type';
  ELSE
    RAISE EXCEPTION 'TEST FAIL: categories.path column missing or wrong type';
  END IF;
END$$;

-- Test 3: Verify datasets table has path column
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'datasets' 
    AND column_name = 'path' 
    AND data_type = 'USER-DEFINED'
  ) THEN
    RAISE NOTICE 'TEST PASS: datasets.path column exists with ltree type';
  ELSE
    RAISE EXCEPTION 'TEST FAIL: datasets.path column missing or wrong type';
  END IF;
END$$;

-- Test 4: Verify triggers exist
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'set_path') THEN
    RAISE NOTICE 'TEST PASS: set_path trigger exists';
  ELSE
    RAISE EXCEPTION 'TEST FAIL: set_path trigger missing';
  END IF;
  
  IF EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_descendant_paths_trigger') THEN
    RAISE NOTICE 'TEST PASS: update_descendant_paths_trigger exists';
  ELSE
    RAISE EXCEPTION 'TEST FAIL: update_descendant_paths_trigger missing';
  END IF;
  
  IF EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'sync_dataset_path') THEN
    RAISE NOTICE 'TEST PASS: sync_dataset_path trigger exists';
  ELSE
    RAISE EXCEPTION 'TEST FAIL: sync_dataset_path trigger missing';
  END IF;
END$$;

-- Test 5: Verify indexes exist
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_indexes 
    WHERE indexname = 'idx_categories_path_gist'
  ) THEN
    RAISE NOTICE 'TEST PASS: GIST index on categories.path exists';
  ELSE
    RAISE EXCEPTION 'TEST FAIL: GIST index on categories.path missing';
  END IF;
  
  IF EXISTS (
    SELECT 1 FROM pg_indexes 
    WHERE indexname = 'idx_categories_variable_overrides_gin'
  ) THEN
    RAISE NOTICE 'TEST PASS: GIN index on categories.variable_overrides exists';
  ELSE
    RAISE EXCEPTION 'TEST FAIL: GIN index on categories.variable_overrides missing';
  END IF;
END$$;

-- Test 6: Test trigger functionality with sample data
DO $$
DECLARE
    test_user_id UUID;
    root_id INTEGER;
    child_id INTEGER;
    grandchild_id INTEGER;
    root_path ltree;
    child_path ltree;
    grandchild_path ltree;
BEGIN
    -- Get a test user
    SELECT id INTO test_user_id FROM auth.users LIMIT 1;
    
    IF test_user_id IS NULL THEN
        RAISE EXCEPTION 'TEST FAIL: No users found for testing';
    END IF;
    
    -- Insert test categories
    INSERT INTO categories (user_id, name, description, parent_category_id) 
    VALUES (test_user_id, 'Test Root', 'Test root category', NULL)
    RETURNING id INTO root_id;
    
    INSERT INTO categories (user_id, name, description, parent_category_id) 
    VALUES (test_user_id, 'Test Child', 'Test child category', root_id)
    RETURNING id INTO child_id;
    
    INSERT INTO categories (user_id, name, description, parent_category_id) 
    VALUES (test_user_id, 'Test Grandchild', 'Test grandchild category', child_id)
    RETURNING id INTO grandchild_id;
    
    -- Verify paths were set correctly
    SELECT path INTO root_path FROM categories WHERE id = root_id;
    SELECT path INTO child_path FROM categories WHERE id = child_id;
    SELECT path INTO grandchild_path FROM categories WHERE id = grandchild_id;
    
    IF root_path = root_id::text::ltree THEN
        RAISE NOTICE 'TEST PASS: Root category path correct: %', root_path;
    ELSE
        RAISE EXCEPTION 'TEST FAIL: Root category path incorrect. Expected: %, Got: %', 
            root_id::text::ltree, root_path;
    END IF;
    
    IF child_path = (root_id::text || '.' || child_id::text)::ltree THEN
        RAISE NOTICE 'TEST PASS: Child category path correct: %', child_path;
    ELSE
        RAISE EXCEPTION 'TEST FAIL: Child category path incorrect. Expected: %, Got: %', 
            (root_id::text || '.' || child_id::text)::ltree, child_path;
    END IF;
    
    IF grandchild_path = (root_id::text || '.' || child_id::text || '.' || grandchild_id::text)::ltree THEN
        RAISE NOTICE 'TEST PASS: Grandchild category path correct: %', grandchild_path;
    ELSE
        RAISE EXCEPTION 'TEST FAIL: Grandchild category path incorrect. Expected: %, Got: %', 
            (root_id::text || '.' || child_id::text || '.' || grandchild_id::text)::ltree, grandchild_path;
    END IF;
    
    -- Test moving a category
    UPDATE categories SET parent_category_id = NULL WHERE id = child_id;
    
    -- Verify paths updated
    SELECT path INTO child_path FROM categories WHERE id = child_id;
    SELECT path INTO grandchild_path FROM categories WHERE id = grandchild_id;
    
    IF child_path = child_id::text::ltree THEN
        RAISE NOTICE 'TEST PASS: Child category path updated correctly after move: %', child_path;
    ELSE
        RAISE EXCEPTION 'TEST FAIL: Child category path not updated correctly after move. Expected: %, Got: %', 
            child_id::text::ltree, child_path;
    END IF;
    
    IF grandchild_path = (child_id::text || '.' || grandchild_id::text)::ltree THEN
        RAISE NOTICE 'TEST PASS: Grandchild category path updated correctly after parent move: %', grandchild_path;
    ELSE
        RAISE EXCEPTION 'TEST FAIL: Grandchild category path not updated correctly after parent move. Expected: %, Got: %', 
            (child_id::text || '.' || grandchild_id::text)::ltree, grandchild_path;
    END IF;
    
    -- Clean up test data
    DELETE FROM categories WHERE id IN (root_id, child_id, grandchild_id);
    
    RAISE NOTICE 'TEST PASS: All trigger functionality tests passed';
END$$;

-- Test 7: Test ltree queries work
DO $$
DECLARE
    test_user_id UUID;
    root_id INTEGER;
    child_id INTEGER;
    grandchild_id INTEGER;
    query_result INTEGER;
BEGIN
    -- Get a test user
    SELECT id INTO test_user_id FROM auth.users LIMIT 1;
    
    -- Insert test categories
    INSERT INTO categories (user_id, name, description, parent_category_id) 
    VALUES (test_user_id, 'Query Test Root', 'Test root category', NULL)
    RETURNING id INTO root_id;
    
    INSERT INTO categories (user_id, name, description, parent_category_id) 
    VALUES (test_user_id, 'Query Test Child', 'Test child category', root_id)
    RETURNING id INTO child_id;
    
    INSERT INTO categories (user_id, name, description, parent_category_id) 
    VALUES (test_user_id, 'Query Test Grandchild', 'Test grandchild category', child_id)
    RETURNING id INTO grandchild_id;
    
    -- Test ancestor query
    SELECT COUNT(*) INTO query_result
    FROM categories 
    WHERE path <@ (SELECT path FROM categories WHERE id = root_id);
    
    IF query_result = 3 THEN
        RAISE NOTICE 'TEST PASS: Ancestor query returned correct count: %', query_result;
    ELSE
        RAISE EXCEPTION 'TEST FAIL: Ancestor query returned wrong count. Expected: 3, Got: %', query_result;
    END IF;
    
    -- Test descendant query
    SELECT COUNT(*) INTO query_result
    FROM categories 
    WHERE (SELECT path FROM categories WHERE id = root_id) @> path;
    
    IF query_result = 3 THEN
        RAISE NOTICE 'TEST PASS: Descendant query returned correct count: %', query_result;
    ELSE
        RAISE EXCEPTION 'TEST FAIL: Descendant query returned wrong count. Expected: 3, Got: %', query_result;
    END IF;
    
    -- Clean up test data
    DELETE FROM categories WHERE id IN (root_id, child_id, grandchild_id);
    
    RAISE NOTICE 'TEST PASS: All ltree query tests passed';
END$$;

RAISE NOTICE 'ALL TESTS PASSED: ltree implementation is working correctly';
