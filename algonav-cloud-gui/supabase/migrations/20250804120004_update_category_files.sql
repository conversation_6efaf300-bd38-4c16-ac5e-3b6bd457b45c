-- Migration: Update category_files table structure
-- Description: Adds missing columns and constraints to category_files table

-- Add missing columns if they don't exist
DO $$
BEGIN
  -- Add id column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'category_files' AND column_name = 'id'
  ) THEN
    -- Add id column as primary key
    ALTER TABLE category_files ADD COLUMN id BIGSERIAL;
    -- Note: We'll set it as primary key after adding constraints
    RAISE NOTICE 'Added id column to category_files table';
  ELSE
    RAISE NOTICE 'ID column already exists in category_files table';
  END IF;
  
  -- Add user_id column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'category_files' AND column_name = 'user_id'
  ) THEN
    ALTER TABLE category_files ADD COLUMN user_id UUID;
    RAISE NOTICE 'Added user_id column to category_files table';
  ELSE
    RAISE NOTICE 'user_id column already exists in category_files table';
  END IF;
END$$;

-- Backfill user_id from categories table for existing records
UPDATE category_files 
SET user_id = c.user_id
FROM categories c
WHERE category_files.category_id = c.id
AND category_files.user_id IS NULL;

-- Make user_id NOT NULL after backfill
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM category_files WHERE user_id IS NULL) THEN
    ALTER TABLE category_files ALTER COLUMN user_id SET NOT NULL;
    RAISE NOTICE 'Set user_id column to NOT NULL';
  ELSE
    RAISE WARNING 'Cannot set user_id to NOT NULL - some records still have NULL user_id';
  END IF;
END$$;

-- Add foreign key constraint for user_id if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE table_name = 'category_files' 
    AND constraint_name = 'category_files_user_id_fkey'
  ) THEN
    ALTER TABLE category_files 
    ADD CONSTRAINT category_files_user_id_fkey 
    FOREIGN KEY (user_id) REFERENCES auth.users(id);
    RAISE NOTICE 'Added foreign key constraint for user_id';
  ELSE
    RAISE NOTICE 'Foreign key constraint for user_id already exists';
  END IF;
END$$;

-- Update file_id to reference files table with UUID if needed
-- First check if files table uses UUID or integer
DO $$
DECLARE
    files_id_type text;
BEGIN
    SELECT data_type INTO files_id_type
    FROM information_schema.columns 
    WHERE table_name = 'files' AND column_name = 'id';
    
    IF files_id_type = 'integer' THEN
        RAISE NOTICE 'Files table uses integer IDs, keeping current structure';
        -- Add foreign key constraint if it doesn't exist
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints 
            WHERE table_name = 'category_files' 
            AND constraint_name = 'category_files_file_id_fkey'
        ) THEN
            ALTER TABLE category_files 
            ADD CONSTRAINT category_files_file_id_fkey 
            FOREIGN KEY (file_id) REFERENCES files(id) ON DELETE CASCADE;
            RAISE NOTICE 'Added foreign key constraint for file_id';
        END IF;
    ELSE
        RAISE NOTICE 'Files table uses % IDs', files_id_type;
    END IF;
END$$;

-- Add foreign key constraint for category_id if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE table_name = 'category_files' 
    AND constraint_name = 'category_files_category_id_fkey'
  ) THEN
    ALTER TABLE category_files 
    ADD CONSTRAINT category_files_category_id_fkey 
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE;
    RAISE NOTICE 'Added foreign key constraint for category_id';
  ELSE
    RAISE NOTICE 'Foreign key constraint for category_id already exists';
  END IF;
END$$;

-- Create unique constraint for category_id + file_type if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE table_name = 'category_files' 
    AND constraint_name = 'category_files_category_file_type_unique'
  ) THEN
    ALTER TABLE category_files 
    ADD CONSTRAINT category_files_category_file_type_unique 
    UNIQUE (category_id, file_type);
    RAISE NOTICE 'Added unique constraint for category_id + file_type';
  ELSE
    RAISE NOTICE 'Unique constraint for category_id + file_type already exists';
  END IF;
END$$;

-- Create indexes for performance
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_indexes 
    WHERE schemaname = 'public' 
    AND tablename = 'category_files' 
    AND indexname = 'idx_category_files_user_id'
  ) THEN
    CREATE INDEX idx_category_files_user_id ON category_files(user_id);
    RAISE NOTICE 'Created index on category_files.user_id';
  ELSE
    RAISE NOTICE 'Index on category_files.user_id already exists';
  END IF;
  
  IF NOT EXISTS (
    SELECT 1 FROM pg_indexes 
    WHERE schemaname = 'public' 
    AND tablename = 'category_files' 
    AND indexname = 'idx_category_files_file_type'
  ) THEN
    CREATE INDEX idx_category_files_file_type ON category_files(file_type);
    RAISE NOTICE 'Created index on category_files.file_type';
  ELSE
    RAISE NOTICE 'Index on category_files.file_type already exists';
  END IF;
END$$;
