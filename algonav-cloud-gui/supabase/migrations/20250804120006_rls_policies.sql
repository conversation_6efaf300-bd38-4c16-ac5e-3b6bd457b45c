-- Migration: Create and update RLS policies for ltree support
-- Description: Adds RLS policies that work with the new ltree path structure

-- Enable RLS on categories table if not already enabled
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist and recreate them
DROP POLICY IF EXISTS "Users can view their own categories" ON categories;
DROP POLICY IF EXISTS "Users can insert their own categories" ON categories;
DROP POLICY IF EXISTS "Users can update their own categories" ON categories;
DROP POLICY IF EXISTS "Users can delete their own categories" ON categories;

-- Create comprehensive RLS policies for categories
CREATE POLICY "Users can view their own categories"
ON categories FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own categories"
ON categories FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own categories"
ON categories FOR UPDATE
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own categories"
ON categories FOR DELETE
USING (auth.uid() = user_id);

-- Enable RLS on datasets table if not already enabled
ALTER TABLE datasets ENABLE ROW LEVEL SECURITY;

-- Update datasets policies to work with category paths
DROP POLICY IF EXISTS "Users can view their own datasets" ON datasets;
DROP POLICY IF EXISTS "Users can insert their own datasets" ON datasets;
DROP POLICY IF EXISTS "Users can update their own datasets" ON datasets;
DROP POLICY IF EXISTS "Users can delete their own datasets" ON datasets;

CREATE POLICY "Users can view their own datasets"
ON datasets FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own datasets"
ON datasets FOR INSERT
WITH CHECK (
  auth.uid() = user_id 
  AND (
    category_id IS NULL 
    OR EXISTS (
      SELECT 1 FROM categories 
      WHERE id = category_id AND user_id = auth.uid()
    )
  )
);

CREATE POLICY "Users can update their own datasets"
ON datasets FOR UPDATE
USING (auth.uid() = user_id)
WITH CHECK (
  auth.uid() = user_id 
  AND (
    category_id IS NULL 
    OR EXISTS (
      SELECT 1 FROM categories 
      WHERE id = category_id AND user_id = auth.uid()
    )
  )
);

CREATE POLICY "Users can delete their own datasets"
ON datasets FOR DELETE
USING (auth.uid() = user_id);

-- Enable RLS on category_files table if not already enabled
ALTER TABLE category_files ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for category_files
DROP POLICY IF EXISTS "Users can view their own category files" ON category_files;
DROP POLICY IF EXISTS "Users can insert their own category files" ON category_files;
DROP POLICY IF EXISTS "Users can update their own category files" ON category_files;
DROP POLICY IF EXISTS "Users can delete their own category files" ON category_files;

CREATE POLICY "Users can view their own category files"
ON category_files FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own category files"
ON category_files FOR INSERT
WITH CHECK (
  auth.uid() = user_id 
  AND EXISTS (
    SELECT 1 FROM categories 
    WHERE id = category_id AND user_id = auth.uid()
  )
  AND EXISTS (
    SELECT 1 FROM files 
    WHERE id = file_id AND user_id = auth.uid()
  )
);

CREATE POLICY "Users can update their own category files"
ON category_files FOR UPDATE
USING (auth.uid() = user_id)
WITH CHECK (
  auth.uid() = user_id 
  AND EXISTS (
    SELECT 1 FROM categories 
    WHERE id = category_id AND user_id = auth.uid()
  )
  AND EXISTS (
    SELECT 1 FROM files 
    WHERE id = file_id AND user_id = auth.uid()
  )
);

CREATE POLICY "Users can delete their own category files"
ON category_files FOR DELETE
USING (auth.uid() = user_id);

-- Add comments for documentation
COMMENT ON POLICY "Users can view their own categories" ON categories IS 'Allow users to view only their own categories';
COMMENT ON POLICY "Users can insert their own categories" ON categories IS 'Allow users to insert categories with their own user_id';
COMMENT ON POLICY "Users can update their own categories" ON categories IS 'Allow users to update only their own categories';
COMMENT ON POLICY "Users can delete their own categories" ON categories IS 'Allow users to delete only their own categories';

COMMENT ON POLICY "Users can view their own datasets" ON datasets IS 'Allow users to view only their own datasets';
COMMENT ON POLICY "Users can insert their own datasets" ON datasets IS 'Allow users to insert datasets with their own user_id and valid category ownership';
COMMENT ON POLICY "Users can update their own datasets" ON datasets IS 'Allow users to update only their own datasets with valid category ownership';
COMMENT ON POLICY "Users can delete their own datasets" ON datasets IS 'Allow users to delete only their own datasets';

COMMENT ON POLICY "Users can view their own category files" ON category_files IS 'Allow users to view only their own category files';
COMMENT ON POLICY "Users can insert their own category files" ON category_files IS 'Allow users to insert category files with valid ownership of both category and file';
COMMENT ON POLICY "Users can update their own category files" ON category_files IS 'Allow users to update only their own category files with valid ownership';
COMMENT ON POLICY "Users can delete their own category files" ON category_files IS 'Allow users to delete only their own category files';
