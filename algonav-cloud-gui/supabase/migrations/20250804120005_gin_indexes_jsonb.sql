-- Migration: Create GIN indexes for JSONB columns
-- Description: Adds GIN indexes for efficient JSONB queries on variable_overrides columns

-- Create GIN index on categories.variable_overrides
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_indexes 
    WHERE schemaname = 'public' 
    AND tablename = 'categories' 
    AND indexname = 'idx_categories_variable_overrides_gin'
  ) THEN
    CREATE INDEX idx_categories_variable_overrides_gin 
    ON categories USING GIN(variable_overrides);
    RAISE NOTICE 'Created GIN index on categories.variable_overrides';
  ELSE
    RAISE NOTICE 'GIN index on categories.variable_overrides already exists';
  END IF;
END$$;

-- Create GIN index on datasets.variable_overrides
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_indexes 
    WHERE schemaname = 'public' 
    AND tablename = 'datasets' 
    AND indexname = 'idx_datasets_variable_overrides_gin'
  ) THEN
    CREATE INDEX idx_datasets_variable_overrides_gin 
    ON datasets USING GIN(variable_overrides);
    RAISE NOTICE 'Created GIN index on datasets.variable_overrides';
  ELSE
    RAISE NOTICE 'GIN index on datasets.variable_overrides already exists';
  END IF;
END$$;

-- Create GIN index on global_job_templates.template_data
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_indexes 
    WHERE schemaname = 'public' 
    AND tablename = 'global_job_templates' 
    AND indexname = 'idx_global_job_templates_template_data_gin'
  ) THEN
    CREATE INDEX idx_global_job_templates_template_data_gin 
    ON global_job_templates USING GIN(template_data);
    RAISE NOTICE 'Created GIN index on global_job_templates.template_data';
  ELSE
    RAISE NOTICE 'GIN index on global_job_templates.template_data already exists';
  END IF;
END$$;

-- Create GIN index on global_job_templates.vars
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_indexes 
    WHERE schemaname = 'public' 
    AND tablename = 'global_job_templates' 
    AND indexname = 'idx_global_job_templates_vars_gin'
  ) THEN
    CREATE INDEX idx_global_job_templates_vars_gin 
    ON global_job_templates USING GIN(vars);
    RAISE NOTICE 'Created GIN index on global_job_templates.vars';
  ELSE
    RAISE NOTICE 'GIN index on global_job_templates.vars already exists';
  END IF;
END$$;

-- Create GIN index on tasks.job_json
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_indexes 
    WHERE schemaname = 'public' 
    AND tablename = 'tasks' 
    AND indexname = 'idx_tasks_job_json_gin'
  ) THEN
    CREATE INDEX idx_tasks_job_json_gin 
    ON tasks USING GIN(job_json);
    RAISE NOTICE 'Created GIN index on tasks.job_json';
  ELSE
    RAISE NOTICE 'GIN index on tasks.job_json already exists';
  END IF;
END$$;

-- Create GIN index on tasks.workervars
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_indexes 
    WHERE schemaname = 'public' 
    AND tablename = 'tasks' 
    AND indexname = 'idx_tasks_workervars_gin'
  ) THEN
    CREATE INDEX idx_tasks_workervars_gin 
    ON tasks USING GIN(workervars);
    RAISE NOTICE 'Created GIN index on tasks.workervars';
  ELSE
    RAISE NOTICE 'GIN index on tasks.workervars already exists';
  END IF;
END$$;

-- Create GIN index on tasks.vars
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_indexes 
    WHERE schemaname = 'public' 
    AND tablename = 'tasks' 
    AND indexname = 'idx_tasks_vars_gin'
  ) THEN
    CREATE INDEX idx_tasks_vars_gin 
    ON tasks USING GIN(vars);
    RAISE NOTICE 'Created GIN index on tasks.vars';
  ELSE
    RAISE NOTICE 'GIN index on tasks.vars already exists';
  END IF;
END$$;

-- Create GIN index on tasks.result
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_indexes 
    WHERE schemaname = 'public' 
    AND tablename = 'tasks' 
    AND indexname = 'idx_tasks_result_gin'
  ) THEN
    CREATE INDEX idx_tasks_result_gin 
    ON tasks USING GIN(result);
    RAISE NOTICE 'Created GIN index on tasks.result';
  ELSE
    RAISE NOTICE 'GIN index on tasks.result already exists';
  END IF;
END$$;

-- Add comments for documentation
COMMENT ON INDEX idx_categories_variable_overrides_gin IS 'GIN index for efficient JSONB queries on categories.variable_overrides';
COMMENT ON INDEX idx_datasets_variable_overrides_gin IS 'GIN index for efficient JSONB queries on datasets.variable_overrides';
COMMENT ON INDEX idx_global_job_templates_template_data_gin IS 'GIN index for efficient JSONB queries on global_job_templates.template_data';
COMMENT ON INDEX idx_global_job_templates_vars_gin IS 'GIN index for efficient JSONB queries on global_job_templates.vars';
COMMENT ON INDEX idx_tasks_job_json_gin IS 'GIN index for efficient JSONB queries on tasks.job_json';
COMMENT ON INDEX idx_tasks_workervars_gin IS 'GIN index for efficient JSONB queries on tasks.workervars';
COMMENT ON INDEX idx_tasks_vars_gin IS 'GIN index for efficient JSONB queries on tasks.vars';
COMMENT ON INDEX idx_tasks_result_gin IS 'GIN index for efficient JSONB queries on tasks.result';
