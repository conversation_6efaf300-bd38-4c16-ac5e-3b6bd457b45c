-- Migration: Remove duplicate job_templates table
-- Description: Remove job_templates table with test data and ensure all references use global_job_templates

BEGIN;

-- Remove job_templates table if it exists (contains only test data)
DO $$
DECLARE
    has_dependencies BOOLEAN := FALSE;
    table_exists BOOLEAN := FALSE;
BEGIN
    -- Check if table exists
    SELECT EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_schema = 'public' AND table_name = 'job_templates'
    ) INTO table_exists;

    IF table_exists THEN
        RAISE NOTICE 'job_templates table exists, checking for dependencies...';

        -- Check if any table references job_templates
        SELECT EXISTS (
            SELECT 1 FROM information_schema.table_constraints AS tc
            JOIN information_schema.key_column_usage AS kcu
                ON tc.constraint_name = kcu.constraint_name
            JOIN information_schema.constraint_column_usage AS ccu
                ON ccu.constraint_name = tc.constraint_name
            WHERE tc.constraint_type = 'FOREIGN KEY'
            AND ccu.table_name = 'job_templates'
        ) INTO has_dependencies;

        IF has_dependencies THEN
            RAISE EXCEPTION 'job_templates table has dependencies, cannot remove safely';
        ELSE
            RAISE NOTICE 'job_templates table has no dependencies, safe to remove';
            DROP TABLE job_templates;
            RAISE NOTICE 'Successfully removed job_templates table with test data';
        END IF;
    ELSE
        RAISE NOTICE 'job_templates table does not exist, no action needed';
    END IF;
END$$;

-- Verify that all job template references use global_job_templates
DO $$
BEGIN
    -- Check job_template_files references
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints AS tc 
        JOIN information_schema.key_column_usage AS kcu
            ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage AS ccu
            ON ccu.constraint_name = tc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY' 
        AND tc.table_name = 'job_template_files'
        AND ccu.table_name = 'global_job_templates'
    ) THEN
        RAISE NOTICE 'VERIFIED: job_template_files correctly references global_job_templates';
    ELSE
        RAISE WARNING 'ISSUE: job_template_files does not reference global_job_templates';
    END IF;
    
    -- Check category_job_templates references
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints AS tc 
        JOIN information_schema.key_column_usage AS kcu
            ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage AS ccu
            ON ccu.constraint_name = tc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY' 
        AND tc.table_name = 'category_job_templates'
        AND ccu.table_name = 'global_job_templates'
    ) THEN
        RAISE NOTICE 'VERIFIED: category_job_templates correctly references global_job_templates';
    ELSE
        RAISE WARNING 'ISSUE: category_job_templates does not reference global_job_templates';
    END IF;
    
    -- Check tasks table references
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints AS tc 
        JOIN information_schema.key_column_usage AS kcu
            ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage AS ccu
            ON ccu.constraint_name = tc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY' 
        AND tc.table_name = 'tasks'
        AND ccu.table_name = 'global_job_templates'
    ) THEN
        RAISE NOTICE 'VERIFIED: tasks table correctly references global_job_templates';
    ELSE
        RAISE WARNING 'ISSUE: tasks table does not reference global_job_templates';
    END IF;
END$$;

-- Verify GIN indexes on global_job_templates exist
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE schemaname = 'public' 
        AND tablename = 'global_job_templates' 
        AND indexname = 'idx_global_job_templates_template_data_gin'
    ) THEN
        RAISE NOTICE 'VERIFIED: GIN index on global_job_templates.template_data exists';
    ELSE
        RAISE WARNING 'ISSUE: GIN index on global_job_templates.template_data missing';
    END IF;
    
    IF EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE schemaname = 'public' 
        AND tablename = 'global_job_templates' 
        AND indexname = 'idx_global_job_templates_vars_gin'
    ) THEN
        RAISE NOTICE 'VERIFIED: GIN index on global_job_templates.vars exists';
    ELSE
        RAISE WARNING 'ISSUE: GIN index on global_job_templates.vars missing';
    END IF;
END$$;

-- Add comments for documentation
COMMENT ON TABLE global_job_templates IS 'Global job templates used throughout the system - this is the primary job template table';

COMMIT;
