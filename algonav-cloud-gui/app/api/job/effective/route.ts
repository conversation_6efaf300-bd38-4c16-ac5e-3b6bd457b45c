import { withAuth } from '@/lib/api/withAuth';
import { NextResponse } from 'next/server';
import { createClient } from "@/utils/supabase/server";

export const POST = withAuth(async (userId, request) => {
    const supabase = createClient();
    
    try {
        const { templateId, datasetIds } = await request.json();

        // Validate required fields
        if (!templateId || typeof templateId !== 'number' || templateId <= 0) {
            return NextResponse.json({ error: 'Valid templateId is required' }, { status: 400 });
        }

        if (!datasetIds || !Array.isArray(datasetIds) || datasetIds.length === 0) {
            return NextResponse.json({ error: 'datasetIds array is required and cannot be empty' }, { status: 400 });
        }

        // Validate all dataset IDs are positive numbers
        if (!datasetIds.every(id => typeof id === 'number' && id > 0)) {
            return NextResponse.json({ error: 'All dataset IDs must be positive numbers' }, { status: 400 });
        }

        // Check for duplicate dataset IDs
        if (new Set(datasetIds).size !== datasetIds.length) {
            return NextResponse.json({ error: 'Dataset IDs must be unique' }, { status: 400 });
        }

        // Try to use RPC function first
        const { data: rpcData, error: rpcError } = await supabase.rpc('merge_job_vars', {
            p_template_id: templateId,
            p_dataset_ids: datasetIds
        });

        if (!rpcError && rpcData) {
            return NextResponse.json({ success: true, data: rpcData });
        }

        // Fallback implementation if RPC doesn't exist
        // For now, return a mock response structure
        console.warn('merge_job_vars RPC not found, using fallback implementation');
        
        // Verify datasets exist and belong to user
        const { data: datasets, error: datasetError } = await supabase
            .from('datasets')
            .select('id, variable_overrides, path')
            .eq('user_id', userId)
            .in('id', datasetIds);

        if (datasetError) {
            return NextResponse.json({ error: datasetError.message }, { status: 500 });
        }

        if (!datasets || datasets.length !== datasetIds.length) {
            return NextResponse.json({ error: 'One or more datasets not found or not accessible' }, { status: 404 });
        }

        // Get template data from global_job_templates
        const { data: template, error: templateError } = await supabase
            .from('global_job_templates')
            .select('vars')
            .eq('id', templateId)
            .single();

        if (templateError) {
            return NextResponse.json({ error: 'Template not found or not accessible' }, { status: 404 });
        }

        // Simple merge: template vars + dataset overrides
        const results = datasets.map(dataset => ({
            datasetId: dataset.id,
            vars: {
                ...(template?.vars || {}),
                ...(dataset.variable_overrides || {})
            }
        }));

        return NextResponse.json({ success: true, data: results });

    } catch (error) {
        console.error('Job effective endpoint error:', error);
        return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
    }
});
