import { withAuth } from '@/lib/api/withAuth';
import { NextResponse } from 'next/server';
import { createClient } from "@/utils/supabase/server";

export const GET = withAuth(async (userId, request) => {
    const supabase = createClient();
    const { searchParams } = new URL(request.url);
    const key = searchParams.get('key');

    // Validate required parameter
    if (!key || typeof key !== 'string' || key.trim() === '') {
        return NextResponse.json({ error: 'Key parameter is required' }, { status: 400 });
    }

    if (key.length > 64) {
        return NextResponse.json({ error: 'Key parameter must be 64 characters or less' }, { status: 400 });
    }

    try {
        // Query categories that have the key in variable_overrides
        const { data: categoryData, error: categoryError } = await supabase
            .from('categories')
            .select('id, path')
            .eq('user_id', userId)
            .contains('variable_overrides', { [key]: null });

        if (categoryError) {
            return NextResponse.json({ error: categoryError.message }, { status: 500 });
        }

        // Query datasets that have the key in variable_overrides
        const { data: datasetData, error: datasetError } = await supabase
            .from('datasets')
            .select('id, path')
            .eq('user_id', userId)
            .contains('variable_overrides', { [key]: null });

        if (datasetError) {
            return NextResponse.json({ error: datasetError.message }, { status: 500 });
        }

        // Combine and format results
        const results = [
            ...(categoryData || []).map(item => ({
                id: item.id,
                path: item.path,
                kind: 'cat' as const
            })),
            ...(datasetData || []).map(item => ({
                id: item.id,
                path: item.path,
                kind: 'ds' as const
            }))
        ];

        // Sort by path
        results.sort((a, b) => a.path.localeCompare(b.path));

        return NextResponse.json({ success: true, data: results });

    } catch (error) {
        console.error('Var-nodes endpoint error:', error);
        return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
    }
});
